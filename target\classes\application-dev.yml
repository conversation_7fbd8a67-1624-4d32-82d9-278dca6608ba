# Development Profile Configuration

server:
  port: 8080

spring:
  # Database Configuration for Development
  datasource:
    url: *********************************************
    username: postgres
    password: password
    
  # JPA Configuration for Development
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true

# DeepSeek API Configuration for Development
deepseek:
  api:
    base-url: https://api.deepseek.com/v1
    api-key: ${DEEPSEEK_API_KEY:your-dev-deepseek-api-key}
    model: deepseek-coder
    timeout: 30000

# Application Configuration for Development
app:
  file:
    upload-dir: ./dev-results
    temp-dir: ./dev-temp
  task:
    cleanup-hours: 1
    max-concurrent-tasks: 2

# Logging Configuration for Development
logging:
  level:
    com.aicodereview: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
