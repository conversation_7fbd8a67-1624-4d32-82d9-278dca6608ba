package com.aicodereview.service;

import com.aicodereview.config.DeepSeekConfig;
import com.aicodereview.model.deepseek.DeepSeekRequest;
import com.aicodereview.model.deepseek.DeepSeekResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.List;

/**
 * DeepSeek API服务
 */
@Service
public class DeepSeekService {

    private static final Logger logger = LoggerFactory.getLogger(DeepSeekService.class);

    private final WebClient webClient;
    private final DeepSeekConfig config;

    @Autowired
    public DeepSeekService(WebClient deepSeekWebClient, DeepSeekConfig config) {
        this.webClient = deepSeekWebClient;
        this.config = config;
    }

    /**
     * 调用DeepSeek API进行代码评审
     */
    public String reviewCode(String code, String systemPrompt) {
        try {
            DeepSeekRequest request = createRequest(systemPrompt, code);
            
            DeepSeekResponse response = webClient
                    .post()
                    .uri("/chat/completions")
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(DeepSeekResponse.class)
                    .timeout(Duration.ofMillis(config.getTimeout()))
                    .retryWhen(Retry.backoff(3, Duration.ofSeconds(1))
                            .filter(throwable -> throwable instanceof WebClientResponseException.TooManyRequests))
                    .block();

            if (response != null && response.getChoices() != null && !response.getChoices().isEmpty()) {
                return response.getChoices().get(0).getMessage().getContent();
            } else {
                logger.warn("DeepSeek API返回空响应");
                return "评审过程发生错误：API返回空响应";
            }

        } catch (Exception e) {
            logger.error("调用DeepSeek API失败", e);
            return "评审过程发生错误：" + e.getMessage();
        }
    }

    /**
     * 异步调用DeepSeek API
     */
    public Mono<String> reviewCodeAsync(String code, String systemPrompt) {
        DeepSeekRequest request = createRequest(systemPrompt, code);
        
        return webClient
                .post()
                .uri("/chat/completions")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(DeepSeekResponse.class)
                .timeout(Duration.ofMillis(config.getTimeout()))
                .retryWhen(Retry.backoff(3, Duration.ofSeconds(1))
                        .filter(throwable -> throwable instanceof WebClientResponseException.TooManyRequests))
                .map(response -> {
                    if (response.getChoices() != null && !response.getChoices().isEmpty()) {
                        return response.getChoices().get(0).getMessage().getContent();
                    } else {
                        return "评审过程发生错误：API返回空响应";
                    }
                })
                .onErrorReturn(throwable -> {
                    logger.error("调用DeepSeek API失败", throwable);
                    return "评审过程发生错误：" + throwable.getMessage();
                });
    }

    /**
     * 创建DeepSeek请求
     */
    private DeepSeekRequest createRequest(String systemPrompt, String code) {
        List<DeepSeekRequest.Message> messages = List.of(
                new DeepSeekRequest.Message("system", systemPrompt),
                new DeepSeekRequest.Message("user", "代码\n" + code)
        );

        return new DeepSeekRequest(
                config.getModel(),
                messages,
                config.getMaxTokens(),
                config.getTemperature()
        );
    }
}
