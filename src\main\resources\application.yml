server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: ai-code-review-backend
  
  # Database Configuration
  datasource:
    url: ********************************************
    username: postgres
    password: Ceiec4567%%
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # MyBatis-Plus Configuration
  mybatis-plus:
    configuration:
      map-underscore-to-camel-case: true
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    global-config:
      db-config:
        id-type: auto
        logic-delete-field: deleted
        logic-delete-value: 1
        logic-not-delete-value: 0
  
  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
      enabled: true
  
  # Jackson Configuration
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# DeepSeek API Configuration
deepseek:
  api:
    base-url: https://api.deepseek.com/v1
    api-key: ${DEEPSEEK_API_KEY:your-deepseek-api-key}
    model: deepseek-coder
    max-tokens: 2000
    temperature: 0.6
    timeout: 60000

# Application Configuration
app:
  file:
    upload-dir: ./results
    temp-dir: ./temp
  task:
    cleanup-hours: 24
    max-concurrent-tasks: 5

# Logging Configuration
logging:
  level:
    com.aicodereview: DEBUG
    org.springframework.web: INFO
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/code-review-backend.log
    max-size: 100MB
    max-history: 30

# Management Endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
