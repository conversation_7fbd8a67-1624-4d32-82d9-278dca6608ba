package com.aicodereview.service;

import com.aicodereview.dto.StatisticsResponse;
import com.aicodereview.entity.CodeReviewRecord;
import com.aicodereview.repository.CodeReviewRecordRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 统计服务
 */
@Service
@Transactional
public class StatisticsService {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsService.class);

    private final CodeReviewRecordRepository repository;

    @Autowired
    public StatisticsService(CodeReviewRecordRepository repository) {
        this.repository = repository;
    }

    /**
     * 保存代码评审统计记录
     */
    public void saveStatistics(String programmingLanguage, Integer suggestionCount, Integer adoptedCount) {
        try {
            CodeReviewRecord record = new CodeReviewRecord(programmingLanguage, suggestionCount, adoptedCount);
            repository.save(record);
            
            logger.info("保存统计记录: 语言={}, 建议数={}, 采纳数={}", 
                programmingLanguage, suggestionCount, adoptedCount);
                
        } catch (Exception e) {
            logger.error("保存统计记录失败", e);
            throw new RuntimeException("保存统计记录失败", e);
        }
    }

    /**
     * 获取按编程语言分组的统计信息
     */
    @Transactional(readOnly = true)
    public List<StatisticsResponse> getStatisticsByProgrammingLanguage() {
        try {
            List<CodeReviewRecordRepository.StatisticsProjection> projections = 
                repository.getStatisticsByProgrammingLanguage();
            
            List<StatisticsResponse> responses = projections.stream()
                .map(p -> new StatisticsResponse(
                    p.getProgrammingLanguage(),
                    p.getTotalSuggestionCount(),
                    p.getTotalAdoptedCount()
                ))
                .collect(Collectors.toList());
            
            logger.debug("获取统计信息: {} 种编程语言", responses.size());
            return responses;
            
        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            throw new RuntimeException("获取统计信息失败", e);
        }
    }

    /**
     * 分页获取所有记录
     */
    @Transactional(readOnly = true)
    public Page<CodeReviewRecord> getAllRecords(Pageable pageable) {
        try {
            Page<CodeReviewRecord> records = repository.findAllByOrderByCreatedAtDesc(pageable);
            logger.debug("分页获取记录: 页码={}, 大小={}, 总数={}", 
                pageable.getPageNumber(), pageable.getPageSize(), records.getTotalElements());
            return records;
            
        } catch (Exception e) {
            logger.error("分页获取记录失败", e);
            throw new RuntimeException("分页获取记录失败", e);
        }
    }

    /**
     * 获取所有记录
     */
    @Transactional(readOnly = true)
    public List<CodeReviewRecord> getAllRecords() {
        try {
            List<CodeReviewRecord> records = repository.findAll();
            logger.debug("获取所有记录: {} 条", records.size());
            return records;
            
        } catch (Exception e) {
            logger.error("获取所有记录失败", e);
            throw new RuntimeException("获取所有记录失败", e);
        }
    }

    /**
     * 获取记录总数
     */
    @Transactional(readOnly = true)
    public long getTotalRecordCount() {
        try {
            long count = repository.count();
            logger.debug("记录总数: {}", count);
            return count;
            
        } catch (Exception e) {
            logger.error("获取记录总数失败", e);
            throw new RuntimeException("获取记录总数失败", e);
        }
    }
}
