# Test Profile Configuration

spring:
  # 使用内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  # MyBatis-Plus配置
  mybatis-plus:
    configuration:
      map-underscore-to-camel-case: true
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    global-config:
      db-config:
        id-type: auto

# DeepSeek API配置（测试环境）
deepseek:
  api:
    base-url: http://localhost:8080/mock
    api-key: test-api-key
    model: test-model
    timeout: 5000

# 应用配置
app:
  file:
    upload-dir: ./test-results
    temp-dir: ./test-temp

# 日志配置
logging:
  level:
    com.aicodereview: DEBUG
