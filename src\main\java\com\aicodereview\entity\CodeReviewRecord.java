package com.aicodereview.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

/**
 * Code Review Record Entity
 * 对应数据库表 code_review_record
 */
@Entity
@Table(name = "code_review_record")
public class CodeReviewRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "programming_language", nullable = false, length = 50)
    private String programmingLanguage;

    @Column(name = "suggestion_count", nullable = false)
    private Integer suggestionCount;

    @Column(name = "adopted_count", nullable = false)
    private Integer adoptedCount;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // Constructors
    public CodeReviewRecord() {}

    public CodeReviewRecord(String programmingLanguage, Integer suggestionCount, Integer adoptedCount) {
        this.programmingLanguage = programmingLanguage;
        this.suggestionCount = suggestionCount;
        this.adoptedCount = adoptedCount;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProgrammingLanguage() {
        return programmingLanguage;
    }

    public void setProgrammingLanguage(String programmingLanguage) {
        this.programmingLanguage = programmingLanguage;
    }

    public Integer getSuggestionCount() {
        return suggestionCount;
    }

    public void setSuggestionCount(Integer suggestionCount) {
        this.suggestionCount = suggestionCount;
    }

    public Integer getAdoptedCount() {
        return adoptedCount;
    }

    public void setAdoptedCount(Integer adoptedCount) {
        this.adoptedCount = adoptedCount;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "CodeReviewRecord{" +
                "id=" + id +
                ", programmingLanguage='" + programmingLanguage + '\'' +
                ", suggestionCount=" + suggestionCount +
                ", adoptedCount=" + adoptedCount +
                ", createdAt=" + createdAt +
                '}';
    }
}
