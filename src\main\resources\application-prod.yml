# Production Profile Configuration

server:
  port: 8080

spring:
  # Database Configuration for Production
  datasource:
    url: ********************************************
    username: postgres
    password: Ceiec4567%%
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
    
  # JPA Configuration for Production
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        format_sql: false

# DeepSeek API Configuration for Production
deepseek:
  api:
    base-url: https://api.deepseek.com/v1
    api-key: ${DEEPSEEK_API_KEY}
    model: deepseek-coder
    timeout: 60000

# Application Configuration for Production
app:
  file:
    upload-dir: /app/results
    temp-dir: /app/temp
  task:
    cleanup-hours: 24
    max-concurrent-tasks: 10

# Logging Configuration for Production
logging:
  level:
    com.aicodereview: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/code-review-backend.log
    max-size: 100MB
    max-history: 30

# Management Endpoints for Production
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: never
