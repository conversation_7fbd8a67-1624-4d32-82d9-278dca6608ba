package com.aicodereview.repository;

import com.aicodereview.entity.CodeReviewRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Code Review Record Repository
 */
@Repository
public interface CodeReviewRecordRepository extends JpaRepository<CodeReviewRecord, Long> {

    /**
     * 分页查询记录，按创建时间倒序
     */
    Page<CodeReviewRecord> findAllByOrderByCreatedAtDesc(Pageable pageable);

    /**
     * 按编程语言分组统计
     */
    @Query("""
        SELECT r.programmingLanguage as programmingLanguage,
               SUM(r.suggestionCount) as totalSuggestionCount,
               SUM(r.adoptedCount) as totalAdoptedCount
        FROM CodeReviewRecord r
        GROUP BY r.programmingLanguage
        ORDER BY r.programmingLanguage
        """)
    List<StatisticsProjection> getStatisticsByProgrammingLanguage();

    /**
     * 统计投影接口
     */
    interface StatisticsProjection {
        String getProgrammingLanguage();
        Long getTotalSuggestionCount();
        Long getTotalAdoptedCount();
    }
}
